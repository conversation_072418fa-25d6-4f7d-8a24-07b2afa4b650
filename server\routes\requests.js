// routes/requests.js
const express = require('express');
const router = express.Router();
const { body, validationResult, query } = require('express-validator');
const rateLimit = require('express-rate-limit');
const Request = require('../models/Request');
const User = require('../models/User');
const Analytics = require('../models/Analytics');
const Notification = require('../models/Notification');
const { auth, requireVerified } = require('../middleware/auth');
const { canCreateRequest, canRespondToRequest, requireOwnerOrAdmin } = require('../middleware/roles');

// Rate limiters
const createLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 requests per hour
  message: {
    success: false,
    message: 'Too many blood requests created. Please wait before creating another request.'
  }
});

const emergencyLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 2, // 2 emergency requests per hour
  message: {
    success: false,
    message: 'Too many emergency requests. Please wait before creating another emergency request.'
  }
});

// Validation rules
const createRequestValidation = [
  body('bloodGroup')
    .isIn(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])
    .withMessage('Invalid blood group'),
  body('urgency')
    .optional()
    .isIn(['normal', 'urgent', 'critical', 'emergency'])
    .withMessage('Invalid urgency level'),
  body('units')
    .isInt({ min: 1, max: 10 })
    .withMessage('Units must be between 1 and 10'),
  body('location.coordinates')
    .isArray({ min: 2, max: 2 })
    .withMessage('Location coordinates must be [longitude, latitude]'),
  body('patientInfo.name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Patient name is required'),
  body('patientInfo.age')
    .isInt({ min: 0, max: 120 })
    .withMessage('Valid patient age is required'),
  body('patientInfo.gender')
    .isIn(['male', 'female', 'other'])
    .withMessage('Valid gender is required'),
  body('patientInfo.medicalCondition')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Medical condition description is required'),
  body('address.hospitalName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Hospital name is required'),
  body('address.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('address.state')
    .trim()
    .notEmpty()
    .withMessage('State is required'),
  body('requiredBy')
    .isISO8601()
    .withMessage('Valid required by date is needed'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Description must be between 10 and 500 characters'),
  body('contactInfo.primaryContact.name')
    .trim()
    .notEmpty()
    .withMessage('Primary contact name is required'),
  body('contactInfo.primaryContact.phone')
    .isMobilePhone()
    .withMessage('Valid primary contact phone is required')
];

// Create blood request
router.post('/',
  auth,
  requireVerified,
  canCreateRequest,
  createLimiter,
  createRequestValidation,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        bloodGroup, urgency, units, location, patientInfo, address,
        requiredBy, description, notes, contactInfo, isEmergency
      } = req.body;

      // Apply emergency rate limiting if needed
      if (urgency === 'emergency' || isEmergency) {
        // Check emergency rate limit manually
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const recentEmergencyRequests = await Request.countDocuments({
          creator: req.user._id,
          $or: [
            { urgency: 'emergency' },
            { isEmergency: true }
          ],
          createdAt: { $gte: oneHourAgo }
        });

        if (recentEmergencyRequests >= 2) {
          return res.status(429).json({
            success: false,
            message: 'Too many emergency requests. Please wait before creating another emergency request.'
          });
        }
      }

      // Create request
      const requestData = {
        creator: req.user._id,
        bloodGroup,
        urgency: urgency || 'normal',
        units,
        location: {
          type: 'Point',
          coordinates: location.coordinates
        },
        patientInfo,
        address,
        requiredBy: new Date(requiredBy),
        description,
        notes,
        contactInfo,
        isEmergency: urgency === 'emergency' || isEmergency || false,
        priority: urgency === 'emergency' ? 10 : urgency === 'critical' ? 8 : urgency === 'urgent' ? 5 : 1
      };

      const request = new Request(requestData);
      await request.save();

      // Update analytics
      await Analytics.updateRequestStats('new_request', {
        urgency: request.urgency,
        bloodGroup: request.bloodGroup,
        isEmergency: request.isEmergency
      });

      // Find matching donors
      const searchRadius = getSearchRadius(request.urgency);
      const matchingDonors = await findMatchingDonors(request, searchRadius);

      // Send notifications to matching donors
      await notifyMatchingDonors(request, matchingDonors);

      // Broadcast via WebSocket
      const io = req.app.get('io');
      if (io) {
        // Broadcast to all donors in the area
        matchingDonors.forEach(donor => {
          io.to(`user_${donor._id}`).emit('new_blood_request', {
            request: {
              _id: request._id,
              bloodGroup: request.bloodGroup,
              urgency: request.urgency,
              units: request.units,
              location: request.location,
              address: request.address,
              patientInfo: request.patientInfo,
              requiredBy: request.requiredBy,
              description: request.description,
              isEmergency: request.isEmergency,
              createdAt: request.createdAt
            }
          });
        });

        // Broadcast emergency requests to all users
        if (request.isEmergency) {
          io.emit('emergency_alert', {
            request: {
              _id: request._id,
              bloodGroup: request.bloodGroup,
              location: request.location,
              address: request.address,
              urgency: request.urgency
            }
          });
        }
      }

      res.status(201).json({
        success: true,
        message: 'Blood request created successfully',
        data: {
          request,
          matchingDonors: matchingDonors.length,
          notificationsSent: matchingDonors.length
        }
      });

    } catch (error) {
      console.error('Create request error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while creating request'
      });
    }
  }
);

// Helper functions
const getSearchRadius = (urgency) => {
  const radiusMap = {
    emergency: 50000, // 50km
    critical: 30000,  // 30km
    urgent: 20000,    // 20km
    normal: 15000     // 15km
  };
  return radiusMap[urgency] || 15000;
};

const findMatchingDonors = async (request, searchRadius) => {
  try {
    const donors = await User.find({
      role: 'donor',
      bloodGroup: request.bloodGroup,
      available: true,
      isVerified: true,
      isActive: true,
      $or: [
        { cooldownUntil: { $exists: false } },
        { cooldownUntil: { $lt: new Date() } }
      ],
      location: {
        $near: {
          $geometry: request.location,
          $maxDistance: searchRadius
        }
      }
    }).select('_id name phone email location preferences');

    return donors;
  } catch (error) {
    console.error('Error finding matching donors:', error);
    return [];
  }
};

const notifyMatchingDonors = async (request, donors) => {
  try {
    const notifications = donors.map(donor => ({
      recipient: donor._id,
      title: `${request.urgency.toUpperCase()} Blood Request - ${request.bloodGroup}`,
      message: `${request.patientInfo.name} needs ${request.units} unit(s) of ${request.bloodGroup} blood at ${request.address.hospitalName}`,
      type: 'blood_request',
      category: request.urgency === 'emergency' ? 'urgent' : 'important',
      relatedRequest: request._id,
      actionRequired: true,
      actionUrl: `/requests/${request._id}`,
      actionText: 'View Request',
      data: {
        requestId: request._id,
        bloodGroup: request.bloodGroup,
        urgency: request.urgency,
        location: request.location,
        hospitalName: request.address.hospitalName
      }
    }));

    await Notification.insertMany(notifications);
  } catch (error) {
    console.error('Error creating notifications:', error);
  }
};

// Get all requests (with filters)
router.get('/', auth, async (req, res) => {
  try {
    const {
      bloodGroup,
      urgency,
      status,
      lat,
      lng,
      radius = 25000, // 25km default
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = { isActive: true };

    if (bloodGroup) query.bloodGroup = bloodGroup;
    if (urgency) query.urgency = urgency;
    if (status) query.status = status;

    // Location-based search
    if (lat && lng) {
      query.location = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(lng), parseFloat(lat)]
          },
          $maxDistance: parseInt(radius)
        }
      };
    }

    // For donors, only show pending requests
    if (req.user.role === 'donor') {
      query.status = 'pending';
      query.expiresAt = { $gt: new Date() };
    }

    // For patients/hospitals, show their own requests
    if (req.user.role === 'patient' || req.user.role === 'hospital') {
      query.creator = req.user._id;
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const requests = await Request.find(query)
      .populate('creator', 'name role address')
      .populate('acceptedBy.donor', 'name phone email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Request.countDocuments(query);

    res.json({
      success: true,
      data: {
        requests,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching requests'
    });
  }
});

// Get single request
router.get('/:id', auth, async (req, res) => {
  try {
    const request = await Request.findById(req.params.id)
      .populate('creator', 'name role phone email address')
      .populate('acceptedBy.donor', 'name phone email location')
      .populate('timeline.user', 'name role');

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Request not found'
      });
    }

    // Check permissions
    const isOwner = request.creator._id.toString() === req.user._id.toString();
    const isAcceptedDonor = request.acceptedBy.some(
      acceptance => acceptance.donor._id.toString() === req.user._id.toString()
    );
    const isAdmin = req.user.role === 'admin';

    if (!isOwner && !isAcceptedDonor && !isAdmin && req.user.role !== 'donor') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Increment view count
    request.viewCount += 1;
    await request.save({ validateBeforeSave: false });

    res.json({
      success: true,
      data: { request }
    });

  } catch (error) {
    console.error('Get request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching request'
    });
  }
});

// Donor accepts a request
router.post('/:id/accept', auth, canRespondToRequest, async (req, res) => {
  try {
    const { unitsCommitted, notes } = req.body;

    if (!unitsCommitted || unitsCommitted < 1) {
      return res.status(400).json({
        success: false,
        message: 'Units committed must be at least 1'
      });
    }

    const request = await Request.findById(req.params.id);
    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Request not found'
      });
    }

    if (request.status === 'completed' || request.status === 'cancelled') {
      return res.status(400).json({
        success: false,
        message: 'Request is no longer active'
      });
    }

    // Check if donor already accepted
    const existingAcceptance = request.acceptedBy.find(
      acceptance => acceptance.donor.toString() === req.user._id.toString()
    );

    if (existingAcceptance) {
      return res.status(400).json({
        success: false,
        message: 'You have already accepted this request'
      });
    }

    // Add donor acceptance
    request.addDonorAcceptance(req.user._id, unitsCommitted, notes);
    await request.save();

    // Create notification for request creator
    await Notification.create({
      recipient: request.creator,
      title: 'Donor Accepted Your Request',
      message: `${req.user.name} has accepted your blood request and committed ${unitsCommitted} unit(s)`,
      type: 'request_accepted',
      category: 'success',
      relatedRequest: request._id,
      relatedUser: req.user._id,
      actionUrl: `/requests/${request._id}`,
      actionText: 'View Request'
    });

    // WebSocket notification
    const io = req.app.get('io');
    if (io) {
      io.to(`user_${request.creator}`).emit('request_accepted', {
        requestId: request._id,
        donor: {
          id: req.user._id,
          name: req.user.name,
          phone: req.user.phone
        },
        unitsCommitted
      });
    }

    res.json({
      success: true,
      message: 'Request accepted successfully',
      data: { request }
    });

  } catch (error) {
    console.error('Accept request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while accepting request'
    });
  }
});

module.exports = router;
