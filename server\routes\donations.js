// routes/donations.js
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Donation = require('../models/Donation');
const Request = require('../models/Request');
const User = require('../models/User');
const Analytics = require('../models/Analytics');
const Notification = require('../models/Notification');
const { auth, requireVerified } = require('../middleware/auth');
const { canManageDonations, requireOwnerOrAdmin } = require('../middleware/roles');

// Get all donations (with filters)
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      bloodGroup,
      donorId,
      recipientId,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query based on user role
    let query = {};
    
    if (req.user.role === 'donor') {
      query.donor = req.user._id;
    } else if (req.user.role === 'patient' || req.user.role === 'hospital') {
      query.recipient = req.user._id;
    }
    // Admin can see all donations

    // Apply filters
    if (status) query.status = status;
    if (bloodGroup) query.bloodGroup = bloodGroup;
    if (donorId && req.user.role === 'admin') query.donor = donorId;
    if (recipientId && req.user.role === 'admin') query.recipient = recipientId;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const donations = await Donation.find(query)
      .populate('donor', 'name email phone bloodGroup')
      .populate('recipient', 'name email phone role')
      .populate('request', 'bloodGroup urgency address')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Donation.countDocuments(query);

    res.json({
      success: true,
      data: {
        donations,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get donations error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching donations'
    });
  }
});

// Get single donation
router.get('/:id', auth, async (req, res) => {
  try {
    const donation = await Donation.findById(req.params.id)
      .populate('donor', 'name email phone bloodGroup location')
      .populate('recipient', 'name email phone role address')
      .populate('request', 'bloodGroup urgency address patientInfo');

    if (!donation) {
      return res.status(404).json({
        success: false,
        message: 'Donation not found'
      });
    }

    // Check permissions
    const isDonor = donation.donor._id.toString() === req.user._id.toString();
    const isRecipient = donation.recipient._id.toString() === req.user._id.toString();
    const isAdmin = req.user.role === 'admin';

    if (!isDonor && !isRecipient && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { donation }
    });

  } catch (error) {
    console.error('Get donation error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching donation'
    });
  }
});

// Create donation record
router.post('/', auth, requireVerified, canManageDonations, [
  body('requestId').isMongoId().withMessage('Valid request ID is required'),
  body('donorId').isMongoId().withMessage('Valid donor ID is required'),
  body('recipientId').isMongoId().withMessage('Valid recipient ID is required'),
  body('unitsCollected').isInt({ min: 1, max: 4 }).withMessage('Units collected must be 1-4'),
  body('scheduledAt').isISO8601().withMessage('Valid scheduled date is required'),
  body('donationCenter.name').notEmpty().withMessage('Donation center name is required'),
  body('donationCenter.address.city').notEmpty().withMessage('Donation center city is required'),
  body('donationCenter.location.coordinates').isArray({ min: 2, max: 2 }).withMessage('Valid coordinates required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      requestId, donorId, recipientId, unitsCollected, scheduledAt,
      donationCenter, medicalInfo, notes
    } = req.body;

    // Verify request exists and is active
    const request = await Request.findById(requestId);
    if (!request || request.status === 'completed' || request.status === 'cancelled') {
      return res.status(400).json({
        success: false,
        message: 'Invalid or inactive request'
      });
    }

    // Verify donor and recipient exist
    const [donor, recipient] = await Promise.all([
      User.findById(donorId),
      User.findById(recipientId)
    ]);

    if (!donor || !recipient) {
      return res.status(400).json({
        success: false,
        message: 'Invalid donor or recipient'
      });
    }

    // Create donation record
    const donation = new Donation({
      request: requestId,
      donor: donorId,
      recipient: recipientId,
      bloodGroup: request.bloodGroup,
      unitsCollected,
      scheduledAt: new Date(scheduledAt),
      donationCenter,
      medicalInfo,
      notes,
      status: 'scheduled'
    });

    await donation.save();

    // Update analytics
    await Analytics.updateDonationStats('new_donation', {
      bloodGroup: donation.bloodGroup,
      unitsCollected: donation.unitsCollected
    });

    // Create notifications
    await Promise.all([
      Notification.create({
        recipient: donorId,
        title: 'Donation Scheduled',
        message: `Your blood donation has been scheduled for ${new Date(scheduledAt).toLocaleDateString()}`,
        type: 'donation_scheduled',
        category: 'info',
        relatedDonation: donation._id
      }),
      Notification.create({
        recipient: recipientId,
        title: 'Donor Found',
        message: `A donor has been scheduled for your blood request`,
        type: 'donation_scheduled',
        category: 'success',
        relatedDonation: donation._id
      })
    ]);

    res.status(201).json({
      success: true,
      message: 'Donation scheduled successfully',
      data: { donation }
    });

  } catch (error) {
    console.error('Create donation error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating donation'
    });
  }
});

// Update donation status
router.patch('/:id/status', auth, canManageDonations, [
  body('status').isIn(['scheduled', 'in_progress', 'completed', 'cancelled', 'failed']),
  body('notes').optional().isLength({ max: 500 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, notes, medicalInfo, testResults } = req.body;
    const donation = await Donation.findById(req.params.id);

    if (!donation) {
      return res.status(404).json({
        success: false,
        message: 'Donation not found'
      });
    }

    // Check permissions
    const isDonor = donation.donor.toString() === req.user._id.toString();
    const isRecipient = donation.recipient.toString() === req.user._id.toString();
    const isAdmin = req.user.role === 'admin';
    const isHospital = req.user.role === 'hospital';

    if (!isDonor && !isRecipient && !isAdmin && !isHospital) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update donation
    donation.status = status;
    if (notes) donation.notes = notes;
    if (medicalInfo) donation.medicalInfo = { ...donation.medicalInfo, ...medicalInfo };
    if (testResults) donation.bloodBag.testResults = { ...donation.bloodBag.testResults, ...testResults };

    // Set timestamps based on status
    switch (status) {
      case 'in_progress':
        donation.startedAt = new Date();
        break;
      case 'completed':
        donation.completedAt = new Date();
        donation.confirmedByDonor = true;
        await Analytics.updateDonationStats('donation_completed');
        break;
      case 'cancelled':
        donation.cancelledAt = new Date();
        donation.cancelledBy = req.user._id;
        await Analytics.updateDonationStats('donation_cancelled');
        break;
    }

    await donation.save();

    // Create notifications
    const notificationTitle = `Donation ${status.charAt(0).toUpperCase() + status.slice(1)}`;
    const notificationMessage = notes || `Your donation status has been updated to ${status}`;

    await Promise.all([
      Notification.create({
        recipient: donation.donor,
        title: notificationTitle,
        message: notificationMessage,
        type: 'donation_completed',
        category: status === 'completed' ? 'success' : 'info',
        relatedDonation: donation._id
      }),
      Notification.create({
        recipient: donation.recipient,
        title: notificationTitle,
        message: notificationMessage,
        type: 'donation_completed',
        category: status === 'completed' ? 'success' : 'info',
        relatedDonation: donation._id
      })
    ]);

    res.json({
      success: true,
      message: 'Donation status updated successfully',
      data: { donation }
    });

  } catch (error) {
    console.error('Update donation status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating donation status'
    });
  }
});

module.exports = router;
