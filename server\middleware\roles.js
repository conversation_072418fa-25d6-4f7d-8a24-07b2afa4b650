// middleware/roles.js

// Role-based access control middleware
const allowRoles = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${allowedRoles.join(' or ')}. Your role: ${req.user.role}`
      });
    }

    next();
  };
};

// Specific role middlewares
const requireDonor = allowRoles('donor');
const requirePatient = allowRoles('patient');
const requireHospital = allowRoles('hospital');
const requireAdmin = allowRoles('admin');

// Combined role middlewares
const requireDonorOrPatient = allowRoles('donor', 'patient');
const requirePatientOrHospital = allowRoles('patient', 'hospital');
const requireHospitalOrAdmin = allowRoles('hospital', 'admin');
const requireAnyUser = allowRoles('donor', 'patient', 'hospital', 'admin');

// Admin or owner access (user can access their own data or admin can access any)
const requireOwnerOrAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required.'
    });
  }

  const userId = req.params.userId || req.params.id;
  const isOwner = req.user._id.toString() === userId;
  const isAdmin = req.user.role === 'admin';

  if (!isOwner && !isAdmin) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. You can only access your own data or admin privileges required.'
    });
  }

  next();
};

// Check if user can create blood requests
const canCreateRequest = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required.'
    });
  }

  const allowedRoles = ['patient', 'hospital'];
  if (!allowedRoles.includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Only patients and hospitals can create blood requests.'
    });
  }

  if (!req.user.isVerified) {
    return res.status(403).json({
      success: false,
      message: 'Account verification required to create blood requests.'
    });
  }

  next();
};

// Check if user can respond to blood requests
const canRespondToRequest = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required.'
    });
  }

  if (req.user.role !== 'donor') {
    return res.status(403).json({
      success: false,
      message: 'Only donors can respond to blood requests.'
    });
  }

  if (!req.user.isVerified) {
    return res.status(403).json({
      success: false,
      message: 'Account verification required to respond to blood requests.'
    });
  }

  if (!req.user.available) {
    return res.status(403).json({
      success: false,
      message: 'You must be available to respond to blood requests. Please update your availability status.'
    });
  }

  next();
};

// Check if user can manage donations
const canManageDonations = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required.'
    });
  }

  const allowedRoles = ['donor', 'hospital', 'admin'];
  if (!allowedRoles.includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Only donors, hospitals, and admins can manage donations.'
    });
  }

  next();
};

// Rate limiting based on role
const getRoleBasedLimits = (role) => {
  const limits = {
    donor: { windowMs: 15 * 60 * 1000, max: 50 }, // 50 requests per 15 minutes
    patient: { windowMs: 15 * 60 * 1000, max: 30 }, // 30 requests per 15 minutes
    hospital: { windowMs: 15 * 60 * 1000, max: 100 }, // 100 requests per 15 minutes
    admin: { windowMs: 15 * 60 * 1000, max: 200 } // 200 requests per 15 minutes
  };

  return limits[role] || { windowMs: 15 * 60 * 1000, max: 20 }; // Default limit
};

module.exports = {
  allowRoles,
  requireDonor,
  requirePatient,
  requireHospital,
  requireAdmin,
  requireDonorOrPatient,
  requirePatientOrHospital,
  requireHospitalOrAdmin,
  requireAnyUser,
  requireOwnerOrAdmin,
  canCreateRequest,
  canRespondToRequest,
  canManageDonations,
  getRoleBasedLimits
};
