// pages/Requests/Requests.jsx
import { HeartIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

const Requests = () => {
  const { user } = useSelector((state) => state.auth);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Blood Requests
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {user?.role === 'donor' 
              ? 'Find blood requests near you' 
              : 'Manage your blood requests'
            }
          </p>
        </div>
        
        {(user?.role === 'patient' || user?.role === 'hospital') && (
          <Link
            to="/requests/create"
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Request</span>
          </Link>
        )}
      </div>

      {/* Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center py-12">
          <HeartIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Blood Requests Feature
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            This feature is under development. You'll be able to view and manage blood requests here.
          </p>
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4 max-w-md mx-auto">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              Coming soon: Real-time blood request matching, emergency alerts, and donor notifications.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Requests;
