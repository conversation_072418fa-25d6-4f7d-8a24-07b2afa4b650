# LifeSaver - Blood Donation Network

A comprehensive MERN stack application that connects blood donors with patients and hospitals in real-time, featuring emergency alerts, location-based matching, and verified donor networks.

## 🩸 Features

### Core Features (MUST-HAVE)
- **Authentication & User Management**: JWT-based auth with role-based access (Donor/Patient/Hospital/Admin)
- **Emergency Blood Request System**: Real-time blood requests with urgency levels and location-based matching
- **Real-Time Updates**: WebSocket-powered live notifications and emergency alerts
- **Search & Match System**: Advanced filtering by blood group, location, and urgency
- **Donor Availability & Status**: Availability toggle with cooldown system
- **Notifications System**: Real-time popups, email/SMS alerts, and dashboard badges
- **Admin Dashboard**: User management, analytics, and system monitoring
- **Request Status Tracking**: Complete lifecycle tracking from pending to completed
- **Security Features**: JWT auth, input validation, rate limiting, and data encryption

### Nice-to-Have Features
- **Map View**: Interactive maps showing donors and requests (Google Maps/Leaflet.js)
- **Donation History**: Complete donation tracking and history
- **Health & Eligibility Tracker**: Donor health status management
- **Gamification**: Leaderboards, badges, and donor recognition
- **PWA Support**: Offline mode for mobile users

## 🛠 Tech Stack

### Frontend
- **React.js** - Modern component-based UI framework
- **Tailwind CSS** - Utility-first CSS framework
- **Redux Toolkit** - State management
- **Socket.IO Client** - Real-time communication
- **React Router** - Navigation and routing
- **Leaflet.js** - Interactive maps
- **Headless UI** - Accessible UI components

### Backend
- **Node.js + Express.js** - REST API development
- **Socket.IO** - Real-time notifications
- **MongoDB + Mongoose** - Database and ODM
- **JWT** - Authentication and authorization
- **bcrypt.js** - Password hashing
- **Express Rate Limit** - API rate limiting

### Database
- **MongoDB Atlas** - Cloud NoSQL database
- **GeoJSON Indexes** - Location-based queries

### Security & Middleware
- **Helmet.js** - Security headers
- **CORS** - Cross-origin request control
- **Mongo Sanitize** - MongoDB injection prevention

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or Atlas)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lifesaver-blood-donation
   ```

2. **Install server dependencies**
   ```bash
   cd server
   npm install
   ```

3. **Install client dependencies**
   ```bash
   cd ../client
   npm install
   ```

4. **Environment Setup**
   
   **Server (.env)**
   ```bash
   cd ../server
   cp .env.example .env
   # Edit .env with your configuration
   ```
   
   **Client (.env)**
   ```bash
   cd ../client
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Start the application**
   
   **Start the server (Terminal 1)**
   ```bash
   cd server
   npm run dev
   ```
   
   **Start the client (Terminal 2)**
   ```bash
   cd client
   npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

## 📁 Project Structure

```
lifesaver-blood-donation/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── store/         # Redux store and slices
│   │   ├── services/      # API and socket services
│   │   └── utils/         # Utility functions
│   ├── public/            # Static assets
│   └── package.json
├── server/                # Node.js backend
│   ├── models/           # MongoDB models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── sockets/          # Socket.IO handlers
│   ├── config/           # Configuration files
│   └── package.json
└── README.md
```

## 🔧 Configuration

### Environment Variables

**Server Configuration**
- `MONGO_URI` - MongoDB connection string
- `JWT_SECRET` - JWT signing secret
- `PORT` - Server port (default: 5000)
- `CLIENT_URL` - Frontend URL for CORS

**Client Configuration**
- `VITE_API_URL` - Backend API URL
- `VITE_GOOGLE_MAPS_API_KEY` - Google Maps API key

## 🌟 Key Features Walkthrough

### 1. User Registration & Authentication
- Multi-role registration (Donor/Patient/Hospital)
- JWT-based authentication with refresh tokens
- Role-based access control

### 2. Blood Request Flow
```
Patient/Hospital creates request → 
System finds matching donors → 
Real-time notifications sent → 
Donor accepts request → 
Donation scheduled → 
Status tracking until completion
```

### 3. Real-Time Features
- Live blood request notifications
- Emergency alerts for critical requests
- Donor availability updates
- Request status changes

### 4. Admin Dashboard
- User management and verification
- System analytics and monitoring
- Request and donation oversight

## 🔒 Security Features

- JWT authentication with refresh tokens
- Password hashing with bcrypt
- Input validation and sanitization
- Rate limiting for API endpoints
- CORS configuration
- MongoDB injection prevention
- Secure HTTP headers with Helmet

## 📱 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update profile

### Blood Requests
- `GET /api/requests` - Get blood requests
- `POST /api/requests` - Create blood request
- `POST /api/requests/:id/accept` - Accept request
- `GET /api/requests/search` - Search requests

### Admin
- `GET /api/admin/dashboard` - Admin dashboard data
- `GET /api/admin/users` - Manage users
- `PATCH /api/admin/users/:id/status` - Update user status

## 🧪 Testing

```bash
# Run server tests
cd server
npm test

# Run client tests
cd client
npm test
```

## 🚀 Deployment

### Production Build
```bash
# Build client
cd client
npm run build

# Start production server
cd ../server
npm start
```

### Deployment Options
- **Frontend**: Vercel, Netlify
- **Backend**: Render, Railway, AWS, DigitalOcean
- **Database**: MongoDB Atlas

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.

## 🙏 Acknowledgments

- Blood donation organizations for inspiration
- Open source community for amazing tools
- Healthcare professionals for guidance

---

**LifeSaver** - Saving lives, one donation at a time. 🩸❤️
