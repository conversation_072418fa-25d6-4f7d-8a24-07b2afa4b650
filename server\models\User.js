// models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const validator = require('validator');

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    minlength: [2, 'Name must be at least 2 characters'],
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    validate: [validator.isEmail, 'Please provide a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: {
      values: ['donor', 'patient', 'hospital', 'admin'],
      message: 'Role must be either donor, patient, hospital, or admin'
    },
    default: 'donor'
  },
  phone: {
    type: String,
    validate: {
      validator: function(v) {
        return !v || validator.isMobilePhone(v);
      },
      message: 'Please provide a valid phone number'
    }
  },
  bloodGroup: {
    type: String,
    enum: {
      values: ['A+','A-','B+','B-','AB+','AB-','O+','O-'],
      message: 'Invalid blood group'
    },
    required: function() {
      return this.role === 'donor' || this.role === 'patient';
    }
  },

  // Address and Location
  address: {
    street: String,
    city: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, default: 'India' },
    pincode: String
  },

  // GeoJSON Point: coordinates: [lng, lat]
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: {
      type: [Number],
      required: true,
      validate: {
        validator: function(coords) {
          return coords.length === 2 &&
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates'
      }
    }
  },

  // Donor specific fields
  available: { type: Boolean, default: false }, // donor availability
  lastDonationDate: { type: Date },
  cooldownUntil: { type: Date }, // donors cannot donate until this date
  donationCount: { type: Number, default: 0 },

  // Health information for donors
  healthInfo: {
    weight: { type: Number, min: 45 }, // minimum weight for donation
    age: { type: Number, min: 18, max: 65 },
    medicalConditions: [String],
    medications: [String],
    lastHealthCheck: Date,
    isEligible: { type: Boolean, default: true }
  },

  // Hospital specific fields
  hospitalInfo: {
    registrationNumber: String,
    hospitalType: {
      type: String,
      enum: ['government', 'private', 'charitable']
    },
    capacity: Number,
    specializations: [String],
    emergencyContact: String
  },

  // Verification and status
  isVerified: { type: Boolean, default: false },
  isActive: { type: Boolean, default: true },
  verificationToken: String,
  verificationTokenExpire: Date,
  resetPasswordToken: String,
  resetPasswordExpire: Date,

  // Preferences and settings
  preferences: {
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: false },
      push: { type: Boolean, default: true }
    },
    privacy: {
      showLocation: { type: Boolean, default: true },
      showPhone: { type: Boolean, default: false },
      showEmail: { type: Boolean, default: false }
    },
    searchRadius: { type: Number, default: 25, min: 1, max: 200 } // in kilometers
  },

  // Statistics and gamification
  stats: {
    requestsCreated: { type: Number, default: 0 },
    requestsFulfilled: { type: Number, default: 0 },
    livesImpacted: { type: Number, default: 0 },
    badges: [String],
    points: { type: Number, default: 0 }
  },

  // Profile picture
  avatar: {
    url: String,
    publicId: String
  },

  // Last activity tracking
  lastActive: { type: Date, default: Date.now },
  loginCount: { type: Number, default: 0 }

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
UserSchema.index({ location: '2dsphere' });
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ bloodGroup: 1 });
UserSchema.index({ available: 1 });
UserSchema.index({ 'address.city': 1 });
UserSchema.index({ isVerified: 1, isActive: 1 });

// Virtual for full name
UserSchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  return `${addr.street ? addr.street + ', ' : ''}${addr.city}, ${addr.state}, ${addr.country}`;
});

// Virtual for donation eligibility
UserSchema.virtual('canDonate').get(function() {
  if (this.role !== 'donor') return false;
  if (!this.available || !this.isVerified || !this.isActive) return false;
  if (this.cooldownUntil && this.cooldownUntil > new Date()) return false;
  if (this.healthInfo && !this.healthInfo.isEligible) return false;
  return true;
});

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to calculate donation cooldown
UserSchema.methods.calculateCooldown = function() {
  if (this.lastDonationDate) {
    const cooldownDays = process.env.DONATION_COOLDOWN_DAYS || 56;
    const cooldownDate = new Date(this.lastDonationDate);
    cooldownDate.setDate(cooldownDate.getDate() + parseInt(cooldownDays));
    this.cooldownUntil = cooldownDate;
  }
};

// Method to update last active
UserSchema.methods.updateLastActive = function() {
  this.lastActive = new Date();
  return this.save({ validateBeforeSave: false });
};

module.exports = mongoose.model('User', UserSchema);
