// services/api.js
import axios from 'axios';
import { store } from '../store';
import { logoutUser, setCredentials } from '../store/slices/authSlice';
import { addToast } from '../store/slices/uiSlice';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh and errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = localStorage.getItem('refreshToken');
      
      if (refreshToken) {
        try {
          // Try to refresh the token
          const response = await axios.post(
            `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/auth/refresh`,
            { refreshToken }
          );

          const { token, refreshToken: newRefreshToken } = response.data.data;
          
          // Update tokens in localStorage
          localStorage.setItem('token', token);
          localStorage.setItem('refreshToken', newRefreshToken);
          
          // Update Redux store
          const state = store.getState();
          store.dispatch(setCredentials({
            user: state.auth.user,
            token,
            refreshToken: newRefreshToken
          }));

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
          
        } catch (refreshError) {
          // Refresh failed, logout user
          store.dispatch(logoutUser());
          store.dispatch(addToast({
            type: 'error',
            message: 'Session expired. Please login again.'
          }));
          
          // Redirect to login page
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token, logout user
        store.dispatch(logoutUser());
        window.location.href = '/login';
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      store.dispatch(addToast({
        type: 'error',
        message: 'Server error. Please try again later.'
      }));
    } else if (error.response?.status === 403) {
      store.dispatch(addToast({
        type: 'error',
        message: 'Access denied. You do not have permission to perform this action.'
      }));
    } else if (error.response?.status === 404) {
      store.dispatch(addToast({
        type: 'error',
        message: 'Resource not found.'
      }));
    } else if (error.code === 'ECONNABORTED') {
      store.dispatch(addToast({
        type: 'error',
        message: 'Request timeout. Please check your connection.'
      }));
    } else if (!error.response) {
      store.dispatch(addToast({
        type: 'error',
        message: 'Network error. Please check your connection.'
      }));
    }

    return Promise.reject(error);
  }
);

// API methods
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  getCurrentUser: () => api.get('/auth/me'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),
  refreshToken: (refreshToken) => api.post('/auth/refresh', { refreshToken }),
};

export const requestsAPI = {
  getRequests: (params) => api.get('/requests', { params }),
  getRequestById: (id) => api.get(`/requests/${id}`),
  createRequest: (requestData) => api.post('/requests', requestData),
  acceptRequest: (id, data) => api.post(`/requests/${id}/accept`, data),
  updateRequestStatus: (id, data) => api.patch(`/requests/${id}/status`, data),
  searchRequests: (params) => api.get('/requests/search', { params }),
};

export const donationsAPI = {
  getDonations: (params) => api.get('/donations', { params }),
  getDonationById: (id) => api.get(`/donations/${id}`),
  createDonation: (donationData) => api.post('/donations', donationData),
  updateDonationStatus: (id, data) => api.patch(`/donations/${id}/status`, data),
  confirmDonation: (id, data) => api.patch(`/donations/${id}/confirm`, data),
};

export const notificationsAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  getNotificationById: (id) => api.get(`/notifications/${id}`),
  markAsRead: (id) => api.patch(`/notifications/${id}/read`),
  markAllAsRead: () => api.patch('/notifications/mark-all-read'),
  deleteNotification: (id) => api.delete(`/notifications/${id}`),
  getUnreadCount: () => api.get('/notifications/unread/count'),
  getPreferences: () => api.get('/notifications/preferences'),
  updatePreferences: (preferences) => api.put('/notifications/preferences', preferences),
};

export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getUsers: (params) => api.get('/admin/users', { params }),
  getUserById: (id) => api.get(`/admin/users/${id}`),
  updateUserStatus: (id, data) => api.patch(`/admin/users/${id}/status`, data),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  getRequests: (params) => api.get('/admin/requests', { params }),
  updateRequestStatus: (id, data) => api.patch(`/admin/requests/${id}/status`, data),
};

// Utility functions
export const uploadFile = async (file, endpoint) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return api.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const downloadFile = async (url, filename) => {
  const response = await api.get(url, {
    responseType: 'blob',
  });
  
  const blob = new Blob([response.data]);
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(downloadUrl);
};

// Export default api instance
export default api;
