// models/Analytics.js
const mongoose = require('mongoose');

const AnalyticsSchema = new mongoose.Schema({
  // Date for daily analytics
  date: { 
    type: Date, 
    required: true,
    unique: true,
    default: () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return today;
    }
  },
  
  // User statistics
  users: {
    totalUsers: { type: Number, default: 0 },
    newRegistrations: { type: Number, default: 0 },
    activeUsers: { type: Number, default: 0 }, // Users active in last 24h
    verifiedUsers: { type: Number, default: 0 },
    
    byRole: {
      donors: { type: Number, default: 0 },
      patients: { type: Number, default: 0 },
      hospitals: { type: Number, default: 0 },
      admins: { type: Number, default: 0 }
    },
    
    byBloodGroup: {
      'A+': { type: Number, default: 0 },
      'A-': { type: Number, default: 0 },
      'B+': { type: Number, default: 0 },
      'B-': { type: Number, default: 0 },
      'AB+': { type: Number, default: 0 },
      'AB-': { type: Number, default: 0 },
      'O+': { type: Number, default: 0 },
      'O-': { type: Number, default: 0 }
    }
  },
  
  // Request statistics
  requests: {
    totalRequests: { type: Number, default: 0 },
    newRequests: { type: Number, default: 0 },
    completedRequests: { type: Number, default: 0 },
    cancelledRequests: { type: Number, default: 0 },
    pendingRequests: { type: Number, default: 0 },
    emergencyRequests: { type: Number, default: 0 },
    
    byUrgency: {
      normal: { type: Number, default: 0 },
      urgent: { type: Number, default: 0 },
      critical: { type: Number, default: 0 },
      emergency: { type: Number, default: 0 }
    },
    
    byBloodGroup: {
      'A+': { type: Number, default: 0 },
      'A-': { type: Number, default: 0 },
      'B+': { type: Number, default: 0 },
      'B-': { type: Number, default: 0 },
      'AB+': { type: Number, default: 0 },
      'AB-': { type: Number, default: 0 },
      'O+': { type: Number, default: 0 },
      'O-': { type: Number, default: 0 }
    },
    
    averageResponseTime: { type: Number, default: 0 }, // in minutes
    averageCompletionTime: { type: Number, default: 0 }, // in minutes
    fulfillmentRate: { type: Number, default: 0 } // percentage
  },
  
  // Donation statistics
  donations: {
    totalDonations: { type: Number, default: 0 },
    newDonations: { type: Number, default: 0 },
    completedDonations: { type: Number, default: 0 },
    cancelledDonations: { type: Number, default: 0 },
    totalUnitsCollected: { type: Number, default: 0 },
    
    byBloodGroup: {
      'A+': { type: Number, default: 0 },
      'A-': { type: Number, default: 0 },
      'B+': { type: Number, default: 0 },
      'B-': { type: Number, default: 0 },
      'AB+': { type: Number, default: 0 },
      'AB-': { type: Number, default: 0 },
      'O+': { type: Number, default: 0 },
      'O-': { type: Number, default: 0 }
    },
    
    averageDonationTime: { type: Number, default: 0 }, // in minutes
    repeatDonors: { type: Number, default: 0 },
    firstTimeDonors: { type: Number, default: 0 }
  },
  
  // Geographic statistics
  geography: {
    topCities: [{
      city: String,
      state: String,
      requestCount: { type: Number, default: 0 },
      donationCount: { type: Number, default: 0 },
      userCount: { type: Number, default: 0 }
    }],
    
    topStates: [{
      state: String,
      requestCount: { type: Number, default: 0 },
      donationCount: { type: Number, default: 0 },
      userCount: { type: Number, default: 0 }
    }]
  },
  
  // System performance
  system: {
    totalApiCalls: { type: Number, default: 0 },
    averageResponseTime: { type: Number, default: 0 }, // in ms
    errorRate: { type: Number, default: 0 }, // percentage
    uptime: { type: Number, default: 100 }, // percentage
    
    notifications: {
      sent: { type: Number, default: 0 },
      delivered: { type: Number, default: 0 },
      failed: { type: Number, default: 0 }
    }
  },
  
  // Engagement metrics
  engagement: {
    averageSessionDuration: { type: Number, default: 0 }, // in minutes
    pageViews: { type: Number, default: 0 },
    uniqueVisitors: { type: Number, default: 0 },
    bounceRate: { type: Number, default: 0 }, // percentage
    
    features: {
      searchUsage: { type: Number, default: 0 },
      mapViewUsage: { type: Number, default: 0 },
      profileUpdates: { type: Number, default: 0 },
      notificationClicks: { type: Number, default: 0 }
    }
  }
  
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AnalyticsSchema.index({ date: -1 });
AnalyticsSchema.index({ createdAt: -1 });

// Virtual for success rate
AnalyticsSchema.virtual('successRate').get(function() {
  if (this.requests.totalRequests === 0) return 0;
  return (this.requests.completedRequests / this.requests.totalRequests) * 100;
});

// Static method to get or create today's analytics
AnalyticsSchema.statics.getTodaysAnalytics = async function() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let analytics = await this.findOne({ date: today });
  
  if (!analytics) {
    analytics = new this({ date: today });
    await analytics.save();
  }
  
  return analytics;
};

// Static method to update user statistics
AnalyticsSchema.statics.updateUserStats = async function(action, userData = {}) {
  const analytics = await this.getTodaysAnalytics();
  
  switch (action) {
    case 'new_registration':
      analytics.users.newRegistrations += 1;
      analytics.users.totalUsers += 1;
      if (userData.role) {
        analytics.users.byRole[userData.role + 's'] += 1;
      }
      if (userData.bloodGroup) {
        analytics.users.byBloodGroup[userData.bloodGroup] += 1;
      }
      break;
      
    case 'user_verified':
      analytics.users.verifiedUsers += 1;
      break;
      
    case 'user_active':
      analytics.users.activeUsers += 1;
      break;
  }
  
  await analytics.save();
  return analytics;
};

// Static method to update request statistics
AnalyticsSchema.statics.updateRequestStats = async function(action, requestData = {}) {
  const analytics = await this.getTodaysAnalytics();
  
  switch (action) {
    case 'new_request':
      analytics.requests.newRequests += 1;
      analytics.requests.totalRequests += 1;
      if (requestData.urgency) {
        analytics.requests.byUrgency[requestData.urgency] += 1;
      }
      if (requestData.bloodGroup) {
        analytics.requests.byBloodGroup[requestData.bloodGroup] += 1;
      }
      if (requestData.isEmergency) {
        analytics.requests.emergencyRequests += 1;
      }
      break;
      
    case 'request_completed':
      analytics.requests.completedRequests += 1;
      break;
      
    case 'request_cancelled':
      analytics.requests.cancelledRequests += 1;
      break;
  }
  
  await analytics.save();
  return analytics;
};

// Static method to update donation statistics
AnalyticsSchema.statics.updateDonationStats = async function(action, donationData = {}) {
  const analytics = await this.getTodaysAnalytics();
  
  switch (action) {
    case 'new_donation':
      analytics.donations.newDonations += 1;
      analytics.donations.totalDonations += 1;
      if (donationData.bloodGroup) {
        analytics.donations.byBloodGroup[donationData.bloodGroup] += 1;
      }
      if (donationData.unitsCollected) {
        analytics.donations.totalUnitsCollected += donationData.unitsCollected;
      }
      break;
      
    case 'donation_completed':
      analytics.donations.completedDonations += 1;
      break;
      
    case 'donation_cancelled':
      analytics.donations.cancelledDonations += 1;
      break;
  }
  
  await analytics.save();
  return analytics;
};

// Static method to get analytics for date range
AnalyticsSchema.statics.getAnalyticsForRange = function(startDate, endDate) {
  return this.find({
    date: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ date: -1 });
};

module.exports = mongoose.model('Analytics', AnalyticsSchema);
