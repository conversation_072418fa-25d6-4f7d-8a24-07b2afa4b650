// services/socket.js
import { io } from 'socket.io-client';
import { store } from '../store';
import { addNotification, setUnreadCount } from '../store/slices/notificationsSlice';
import { addNewRequest, updateRequestInList } from '../store/slices/requestsSlice';
import { addNewDonation, updateDonationInList } from '../store/slices/donationsSlice';
import { addToast } from '../store/slices/uiSlice';
import { updateUserData } from '../store/slices/authSlice';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  connect() {
    const token = localStorage.getItem('token');
    
    if (!token) {
      console.log('No token found, skipping socket connection');
      return;
    }

    const serverUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5000';
    
    this.socket = io(serverUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('🔌 Socket connected:', this.socket.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      store.dispatch(addToast({
        type: 'success',
        message: 'Connected to real-time updates',
        duration: 3000
      }));
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
      this.handleReconnect();
    });

    // Authentication events
    this.socket.on('connected', (data) => {
      console.log('Socket authenticated:', data);
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
      store.dispatch(addToast({
        type: 'error',
        message: error.message || 'Socket error occurred',
        duration: 5000
      }));
    });

    // Blood request events
    this.socket.on('new_blood_request', (data) => {
      console.log('New blood request received:', data);
      
      store.dispatch(addNewRequest(data.request));
      store.dispatch(addToast({
        type: 'info',
        message: `New ${data.request.urgency} blood request for ${data.request.bloodGroup}`,
        duration: 8000
      }));
    });

    this.socket.on('request_accepted', (data) => {
      console.log('Request accepted:', data);
      
      store.dispatch(updateRequestInList(data.request));
      store.dispatch(addToast({
        type: 'success',
        message: `${data.donor.name} accepted your blood request`,
        duration: 6000
      }));
    });

    this.socket.on('request_status_updated', (data) => {
      console.log('Request status updated:', data);
      
      store.dispatch(updateRequestInList(data.request));
      store.dispatch(addToast({
        type: 'info',
        message: `Request status updated to ${data.status}`,
        duration: 5000
      }));
    });

    // Emergency alerts
    this.socket.on('emergency_alert', (data) => {
      console.log('Emergency alert received:', data);
      
      store.dispatch(addToast({
        type: 'error',
        message: `🚨 EMERGENCY: ${data.request.bloodGroup} blood needed urgently!`,
        duration: 10000
      }));
      
      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification('Emergency Blood Request', {
          body: `${data.request.bloodGroup} blood needed urgently at ${data.request.address.hospitalName}`,
          icon: '/favicon.ico',
          tag: 'emergency-request'
        });
      }
    });

    // Donation events
    this.socket.on('donation_scheduled', (data) => {
      console.log('Donation scheduled:', data);
      
      store.dispatch(addNewDonation(data.donation));
      store.dispatch(addToast({
        type: 'success',
        message: 'Donation has been scheduled successfully',
        duration: 5000
      }));
    });

    this.socket.on('donation_status_updated', (data) => {
      console.log('Donation status updated:', data);
      
      store.dispatch(updateDonationInList(data.donation));
      store.dispatch(addToast({
        type: 'info',
        message: `Donation status updated to ${data.status}`,
        duration: 5000
      }));
    });

    // Notification events
    this.socket.on('new_notification', (data) => {
      console.log('New notification received:', data);
      
      store.dispatch(addNotification(data));
      
      // Show toast for important notifications
      if (data.category === 'urgent' || data.category === 'important') {
        store.dispatch(addToast({
          type: data.category === 'urgent' ? 'error' : 'warning',
          message: data.title,
          duration: 6000
        }));
      }
    });

    this.socket.on('unread_notifications_count', (data) => {
      store.dispatch(setUnreadCount(data.count));
    });

    // User events
    this.socket.on('availability_updated', (data) => {
      console.log('Availability updated:', data);
      
      store.dispatch(updateUserData({
        available: data.available,
        location: data.location
      }));
      
      store.dispatch(addToast({
        type: 'success',
        message: `Availability updated to ${data.available ? 'Available' : 'Unavailable'}`,
        duration: 3000
      }));
    });

    this.socket.on('location_updated', (data) => {
      console.log('Location updated:', data);
      
      store.dispatch(updateUserData({
        location: data.location
      }));
    });

    this.socket.on('account_status_updated', (data) => {
      console.log('Account status updated:', data);
      
      store.dispatch(updateUserData({
        isVerified: data.isVerified,
        isActive: data.isActive
      }));
      
      const message = data.action === 'verify' ? 'Account verified successfully!' :
                     data.action === 'activate' ? 'Account activated!' :
                     data.action === 'deactivate' ? 'Account deactivated' :
                     'Account status updated';
      
      store.dispatch(addToast({
        type: data.action === 'verify' || data.action === 'activate' ? 'success' : 'warning',
        message: message,
        duration: 5000
      }));
    });

    // Donor availability events
    this.socket.on('donor_available', (data) => {
      console.log('Donor became available:', data);
      
      store.dispatch(addToast({
        type: 'info',
        message: `${data.donor.name} is now available for donation`,
        duration: 5000
      }));
    });

    // Typing indicators (for chat features)
    this.socket.on('user_typing', (data) => {
      console.log('User typing:', data);
      // Handle typing indicator UI
    });

    this.socket.on('user_stopped_typing', (data) => {
      console.log('User stopped typing:', data);
      // Handle stop typing indicator UI
    });
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
      
      setTimeout(() => {
        if (!this.isConnected) {
          this.connect();
        }
      }, delay);
    } else {
      store.dispatch(addToast({
        type: 'error',
        message: 'Lost connection to server. Please refresh the page.',
        duration: 10000
      }));
    }
  }

  // Public methods for emitting events
  updateAvailability(available, location) {
    if (this.socket && this.isConnected) {
      this.socket.emit('update_availability', { available, location });
    }
  }

  updateLocation(coordinates) {
    if (this.socket && this.isConnected) {
      this.socket.emit('update_location', { coordinates });
    }
  }

  updateRequestStatus(requestId, status, notes) {
    if (this.socket && this.isConnected) {
      this.socket.emit('update_request_status', { requestId, status, notes });
    }
  }

  joinRequest(requestId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_request', { requestId });
    }
  }

  leaveRequest(requestId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_request', { requestId });
    }
  }

  markNotificationRead(notificationId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('mark_notification_read', { notificationId });
    }
  }

  sendActivity() {
    if (this.socket && this.isConnected) {
      this.socket.emit('activity');
    }
  }

  startTyping(requestId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing_start', { requestId });
    }
  }

  stopTyping(requestId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing_stop', { requestId });
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.reconnectAttempts = 0;
    }
  }

  isSocketConnected() {
    return this.isConnected && this.socket?.connected;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
