// store/slices/requestsSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Async thunks
export const fetchRequests = createAsyncThunk(
  'requests/fetchRequests',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/requests', { params });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch requests');
    }
  }
);

export const fetchRequestById = createAsyncThunk(
  'requests/fetchRequestById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/requests/${id}`);
      return response.data.data.request;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch request');
    }
  }
);

export const createRequest = createAsyncThunk(
  'requests/createRequest',
  async (requestData, { rejectWithValue }) => {
    try {
      const response = await api.post('/requests', requestData);
      return response.data.data.request;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create request');
    }
  }
);

export const acceptRequest = createAsyncThunk(
  'requests/acceptRequest',
  async ({ id, unitsCommitted, notes }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/requests/${id}/accept`, {
        unitsCommitted,
        notes
      });
      return response.data.data.request;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to accept request');
    }
  }
);

export const updateRequestStatus = createAsyncThunk(
  'requests/updateRequestStatus',
  async ({ id, status, notes }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/requests/${id}/status`, {
        status,
        notes
      });
      return response.data.data.request;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update request status');
    }
  }
);

export const searchRequests = createAsyncThunk(
  'requests/searchRequests',
  async (searchParams, { rejectWithValue }) => {
    try {
      const response = await api.get('/requests/search', { params: searchParams });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Search failed');
    }
  }
);

const initialState = {
  requests: [],
  currentRequest: null,
  searchResults: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  },
  filters: {
    bloodGroup: '',
    urgency: '',
    status: '',
    location: null,
    radius: 25
  },
  createRequestSuccess: false,
  acceptRequestSuccess: false,
};

const requestsSlice = createSlice({
  name: 'requests',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentRequest: (state) => {
      state.currentRequest = null;
    },
    clearCreateRequestSuccess: (state) => {
      state.createRequestSuccess = false;
    },
    clearAcceptRequestSuccess: (state) => {
      state.acceptRequestSuccess = false;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        bloodGroup: '',
        urgency: '',
        status: '',
        location: null,
        radius: 25
      };
    },
    addNewRequest: (state, action) => {
      state.requests.unshift(action.payload);
    },
    updateRequestInList: (state, action) => {
      const index = state.requests.findIndex(req => req._id === action.payload._id);
      if (index !== -1) {
        state.requests[index] = action.payload;
      }
    },
    removeRequestFromList: (state, action) => {
      state.requests = state.requests.filter(req => req._id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch requests
      .addCase(fetchRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.requests = action.payload.requests;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Fetch request by ID
      .addCase(fetchRequestById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRequestById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentRequest = action.payload;
        state.error = null;
      })
      .addCase(fetchRequestById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create request
      .addCase(createRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createRequest.fulfilled, (state, action) => {
        state.loading = false;
        state.requests.unshift(action.payload);
        state.createRequestSuccess = true;
        state.error = null;
      })
      .addCase(createRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Accept request
      .addCase(acceptRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(acceptRequest.fulfilled, (state, action) => {
        state.loading = false;
        state.currentRequest = action.payload;
        state.acceptRequestSuccess = true;
        state.error = null;
        
        // Update in requests list if present
        const index = state.requests.findIndex(req => req._id === action.payload._id);
        if (index !== -1) {
          state.requests[index] = action.payload;
        }
      })
      .addCase(acceptRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update request status
      .addCase(updateRequestStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateRequestStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.currentRequest = action.payload;
        state.error = null;
        
        // Update in requests list if present
        const index = state.requests.findIndex(req => req._id === action.payload._id);
        if (index !== -1) {
          state.requests[index] = action.payload;
        }
      })
      .addCase(updateRequestStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Search requests
      .addCase(searchRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.searchResults = action.payload.requests || action.payload;
        state.error = null;
      })
      .addCase(searchRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  clearError,
  clearCurrentRequest,
  clearCreateRequestSuccess,
  clearAcceptRequestSuccess,
  setFilters,
  clearFilters,
  addNewRequest,
  updateRequestInList,
  removeRequestFromList,
} = requestsSlice.actions;

export default requestsSlice.reducer;
