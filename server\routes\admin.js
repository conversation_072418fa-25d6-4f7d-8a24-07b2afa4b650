// routes/admin.js
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Request = require('../models/Request');
const Donation = require('../models/Donation');
const Analytics = require('../models/Analytics');
const Notification = require('../models/Notification');
const { auth } = require('../middleware/auth');
const { requireAdmin } = require('../middleware/roles');

// Apply admin authentication to all routes
router.use(auth);
router.use(requireAdmin);

// Dashboard Analytics
router.get('/dashboard', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);

    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    // Get today's analytics
    const todayAnalytics = await Analytics.getTodaysAnalytics();

    // Get recent analytics for trends
    const weeklyAnalytics = await Analytics.getAnalyticsForRange(lastWeek, today);
    const monthlyAnalytics = await Analytics.getAnalyticsForRange(lastMonth, today);

    // Real-time counts
    const [
      totalUsers,
      totalDonors,
      totalRequests,
      activeRequests,
      completedDonations,
      pendingRequests,
      verifiedUsers,
      availableDonors
    ] = await Promise.all([
      User.countDocuments({ isActive: true }),
      User.countDocuments({ role: 'donor', isActive: true }),
      Request.countDocuments(),
      Request.countDocuments({ status: 'pending', isActive: true }),
      Donation.countDocuments({ status: 'completed' }),
      Request.countDocuments({ status: 'pending' }),
      User.countDocuments({ isVerified: true, isActive: true }),
      User.countDocuments({ role: 'donor', available: true, isVerified: true })
    ]);

    // Blood group distribution
    const bloodGroupStats = await User.aggregate([
      { $match: { role: 'donor', isActive: true } },
      { $group: { _id: '$bloodGroup', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]);

    // Recent activity
    const recentRequests = await Request.find({ isActive: true })
      .populate('creator', 'name role')
      .sort({ createdAt: -1 })
      .limit(10);

    const recentUsers = await User.find({ isActive: true })
      .select('name email role createdAt isVerified')
      .sort({ createdAt: -1 })
      .limit(10);

    // Geographic distribution
    const cityStats = await User.aggregate([
      { $match: { isActive: true } },
      { $group: {
        _id: { city: '$address.city', state: '$address.state' },
        count: { $sum: 1 },
        donors: { $sum: { $cond: [{ $eq: ['$role', 'donor'] }, 1, 0] } },
        hospitals: { $sum: { $cond: [{ $eq: ['$role', 'hospital'] }, 1, 0] } }
      }},
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          totalDonors,
          totalRequests,
          activeRequests,
          completedDonations,
          pendingRequests,
          verifiedUsers,
          availableDonors
        },
        todayStats: todayAnalytics,
        trends: {
          weekly: weeklyAnalytics,
          monthly: monthlyAnalytics
        },
        bloodGroupDistribution: bloodGroupStats,
        recentActivity: {
          requests: recentRequests,
          users: recentUsers
        },
        geography: cityStats
      }
    });

  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard data'
    });
  }
});

// User Management
router.get('/users', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      role,
      isVerified,
      isActive,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    if (role) query.role = role;
    if (isVerified !== undefined) query.isVerified = isVerified === 'true';
    if (isActive !== undefined) query.isActive = isActive === 'true';

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const users = await User.find(query)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching users'
    });
  }
});

// Get single user details
router.get('/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get user's requests and donations
    const [requests, donations] = await Promise.all([
      Request.find({ creator: user._id }).sort({ createdAt: -1 }).limit(10),
      Donation.find({
        $or: [{ donor: user._id }, { recipient: user._id }]
      }).sort({ createdAt: -1 }).limit(10)
    ]);

    res.json({
      success: true,
      data: {
        user,
        activity: {
          requests,
          donations
        }
      }
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user'
    });
  }
});

// Update user status (verify, activate, deactivate)
router.patch('/users/:id/status', [
  body('action').isIn(['verify', 'unverify', 'activate', 'deactivate']).withMessage('Invalid action'),
  body('reason').optional().isLength({ min: 5, max: 200 }).withMessage('Reason must be 5-200 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { action, reason } = req.body;
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from deactivating themselves
    if (action === 'deactivate' && user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot deactivate your own account'
      });
    }

    // Update user status
    switch (action) {
      case 'verify':
        user.isVerified = true;
        await Analytics.updateUserStats('user_verified');
        break;
      case 'unverify':
        user.isVerified = false;
        break;
      case 'activate':
        user.isActive = true;
        break;
      case 'deactivate':
        user.isActive = false;
        user.available = false; // Also set unavailable if donor
        break;
    }

    await user.save();

    // Create notification for user
    await Notification.create({
      recipient: user._id,
      title: `Account ${action}d`,
      message: reason || `Your account has been ${action}d by an administrator`,
      type: 'system_update',
      category: action === 'verify' || action === 'activate' ? 'success' : 'warning'
    });

    // Send real-time notification
    const io = req.app.get('io');
    if (io) {
      io.to(`user_${user._id}`).emit('account_status_updated', {
        action,
        reason,
        isVerified: user.isVerified,
        isActive: user.isActive
      });
    }

    res.json({
      success: true,
      message: `User ${action}d successfully`,
      data: { user: { ...user.toObject(), password: undefined } }
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating user status'
    });
  }
});

// Delete user (soft delete)
router.delete('/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from deleting themselves
    if (user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot delete your own account'
      });
    }

    // Soft delete - deactivate instead of removing
    user.isActive = false;
    user.available = false;
    await user.save();

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting user'
    });
  }
});

// Request Management
router.get('/requests', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      urgency,
      bloodGroup,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    if (status) query.status = status;
    if (urgency) query.urgency = urgency;
    if (bloodGroup) query.bloodGroup = bloodGroup;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const requests = await Request.find(query)
      .populate('creator', 'name email role address')
      .populate('acceptedBy.donor', 'name email phone')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Request.countDocuments(query);

    res.json({
      success: true,
      data: {
        requests,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching requests'
    });
  }
});

// Update request status
router.patch('/requests/:id/status', [
  body('status').isIn(['pending', 'accepted', 'in_progress', 'completed', 'cancelled', 'expired']),
  body('reason').optional().isLength({ min: 5, max: 200 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, reason } = req.body;
    const request = await Request.findById(req.params.id);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Request not found'
      });
    }

    // Update status
    request.status = status;
    request.timeline.push({
      action: status,
      user: req.user._id,
      notes: reason || `Status updated by admin to ${status}`
    });

    if (status === 'completed') {
      request.fulfillment.completedAt = new Date();
      await Analytics.updateRequestStats('request_completed');
    } else if (status === 'cancelled') {
      await Analytics.updateRequestStats('request_cancelled');
    }

    await request.save();

    // Notify request creator
    await Notification.create({
      recipient: request.creator,
      title: 'Request Status Updated',
      message: `Your blood request status has been updated to ${status}`,
      type: 'system_update',
      category: 'info',
      relatedRequest: request._id
    });

    res.json({
      success: true,
      message: 'Request status updated successfully',
      data: { request }
    });

  } catch (error) {
    console.error('Update request status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating request status'
    });
  }
});

module.exports = router;