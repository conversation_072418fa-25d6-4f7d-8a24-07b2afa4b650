// components/Layout/Sidebar.jsx
import { useSelector, useDispatch } from 'react-redux';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  HomeIcon,
  HeartIcon,
  MapPinIcon,
  BellIcon,
  UserIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { setSidebarOpen } from '../../store/slices/uiSlice';

const Sidebar = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { sidebarOpen } = useSelector((state) => state.ui);
  const { user } = useSelector((state) => state.auth);

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, roles: ['donor', 'patient', 'hospital', 'admin'] },
    { name: 'Blood Requests', href: '/requests', icon: HeartIcon, roles: ['donor', 'patient', 'hospital', 'admin'] },
    { name: 'Donations', href: '/donations', icon: DocumentTextIcon, roles: ['donor', 'patient', 'hospital', 'admin'] },
    { name: 'Map View', href: '/map', icon: MapPinIcon, roles: ['donor', 'patient', 'hospital', 'admin'] },
    { name: 'Notifications', href: '/notifications', icon: BellIcon, roles: ['donor', 'patient', 'hospital', 'admin'] },
    { name: 'Profile', href: '/profile', icon: UserIcon, roles: ['donor', 'patient', 'hospital', 'admin'] },
  ];

  const adminNavigation = [
    { name: 'Admin Dashboard', href: '/admin', icon: ChartBarIcon },
    { name: 'Manage Users', href: '/admin/users', icon: UsersIcon },
    { name: 'Manage Requests', href: '/admin/requests', icon: HeartIcon },
  ];

  const filteredNavigation = navigation.filter(item => 
    item.roles.includes(user?.role)
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={`${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } fixed inset-y-0 left-0 z-30 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
              <HeartIcon className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              LifeSaver
            </span>
          </div>
          
          {/* Close button for mobile */}
          <button
            onClick={() => dispatch(setSidebarOpen(false))}
            className="lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-8 px-4">
          <div className="space-y-2">
            {filteredNavigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  className={`${
                    isActive
                      ? 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  } group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors`}
                  onClick={() => {
                    // Close sidebar on mobile after navigation
                    if (window.innerWidth < 1024) {
                      dispatch(setSidebarOpen(false));
                    }
                  }}
                >
                  <item.icon
                    className={`${
                      isActive
                        ? 'text-red-500 dark:text-red-400'
                        : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                    } mr-3 h-5 w-5`}
                  />
                  {item.name}
                </NavLink>
              );
            })}
          </div>

          {/* Admin Section */}
          {user?.role === 'admin' && (
            <div className="mt-8">
              <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Administration
              </h3>
              <div className="mt-2 space-y-2">
                {adminNavigation.map((item) => {
                  const isActive = location.pathname === item.href;
                  return (
                    <NavLink
                      key={item.name}
                      to={item.href}
                      className={`${
                        isActive
                          ? 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      } group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors`}
                      onClick={() => {
                        if (window.innerWidth < 1024) {
                          dispatch(setSidebarOpen(false));
                        }
                      }}
                    >
                      <item.icon
                        className={`${
                          isActive
                            ? 'text-red-500 dark:text-red-400'
                            : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                        } mr-3 h-5 w-5`}
                      />
                      {item.name}
                    </NavLink>
                  );
                })}
              </div>
            </div>
          )}
        </nav>

        {/* User Info */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
              <UserIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {user?.name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                {user?.role}
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
