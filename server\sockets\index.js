// sockets/index.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Request = require('../models/Request');
const Notification = require('../models/Notification');

// Store active connections
const activeConnections = new Map();

const initSockets = (io) => {
  // Socket authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth?.token;

      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user details
      const user = await User.findById(decoded.id).select('-password');
      if (!user || !user.isActive) {
        return next(new Error('User not found or inactive'));
      }

      socket.user = user;
      next();
    } catch (error) {
      console.error('Socket authentication failed:', error.message);
      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', async (socket) => {
    const user = socket.user;
    const userId = user._id.toString();

    console.log(`🔌 Socket connected: ${socket.id} for user ${user.name} (${user.role})`);

    // Join user-specific room
    socket.join(`user_${userId}`);

    // Join role-specific rooms
    socket.join(`role_${user.role}`);

    // Join location-based room (city)
    if (user.address?.city) {
      socket.join(`city_${user.address.city.toLowerCase()}`);
    }

    // Store active connection
    activeConnections.set(userId, {
      socketId: socket.id,
      user: user,
      connectedAt: new Date(),
      lastActivity: new Date()
    });

    // Update user's last active time
    await user.updateLastActive();

    // Send connection confirmation
    socket.emit('connected', {
      message: 'Connected successfully',
      user: {
        id: user._id,
        name: user.name,
        role: user.role,
        isVerified: user.isVerified
      }
    });

    // Send unread notifications count
    const unreadCount = await Notification.getUnreadCount(userId);
    socket.emit('unread_notifications_count', { count: unreadCount });

    // Handle donor availability updates
    socket.on('update_availability', async (data) => {
      try {
        if (user.role !== 'donor') {
          return socket.emit('error', { message: 'Only donors can update availability' });
        }

        const { available, location } = data;

        // Update user availability
        user.available = !!available;

        // Update location if provided
        if (location && location.coordinates && Array.isArray(location.coordinates)) {
          user.location = {
            type: 'Point',
            coordinates: location.coordinates
          };
        }

        await user.save();

        // Confirm update
        socket.emit('availability_updated', {
          available: user.available,
          location: user.location
        });

        // Notify nearby requests if donor became available
        if (available && user.isVerified) {
          await notifyNearbyRequests(user, io);
        }

        console.log(`📍 Donor ${user.name} availability updated: ${available}`);

      } catch (error) {
        console.error('Update availability error:', error);
        socket.emit('error', { message: 'Failed to update availability' });
      }
    });

    // Handle location updates
    socket.on('update_location', async (data) => {
      try {
        const { coordinates } = data;

        if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2) {
          return socket.emit('error', { message: 'Invalid coordinates' });
        }

        user.location = {
          type: 'Point',
          coordinates: coordinates
        };

        await user.save();

        socket.emit('location_updated', { location: user.location });
        console.log(`📍 User ${user.name} location updated`);

      } catch (error) {
        console.error('Update location error:', error);
        socket.emit('error', { message: 'Failed to update location' });
      }
    });

    // Handle request status updates
    socket.on('update_request_status', async (data) => {
      try {
        const { requestId, status, notes } = data;

        const request = await Request.findById(requestId);
        if (!request) {
          return socket.emit('error', { message: 'Request not found' });
        }

        // Check permissions
        const isOwner = request.creator.toString() === userId;
        const isAcceptedDonor = request.acceptedBy.some(
          acceptance => acceptance.donor.toString() === userId
        );

        if (!isOwner && !isAcceptedDonor && user.role !== 'admin') {
          return socket.emit('error', { message: 'Permission denied' });
        }

        // Update status
        request.status = status;
        if (notes) {
          request.timeline.push({
            action: status,
            user: userId,
            notes: notes
          });
        }

        await request.save();

        // Notify relevant parties
        const notificationData = {
          requestId: request._id,
          status: status,
          updatedBy: user.name,
          notes: notes
        };

        // Notify request creator
        io.to(`user_${request.creator}`).emit('request_status_updated', notificationData);

        // Notify accepted donors
        request.acceptedBy.forEach(acceptance => {
          io.to(`user_${acceptance.donor}`).emit('request_status_updated', notificationData);
        });

        socket.emit('request_status_update_success', { requestId, status });

      } catch (error) {
        console.error('Update request status error:', error);
        socket.emit('error', { message: 'Failed to update request status' });
      }
    });

    // Handle typing indicators for chat
    socket.on('typing_start', (data) => {
      const { requestId } = data;
      socket.to(`request_${requestId}`).emit('user_typing', {
        userId: userId,
        userName: user.name
      });
    });

    socket.on('typing_stop', (data) => {
      const { requestId } = data;
      socket.to(`request_${requestId}`).emit('user_stopped_typing', {
        userId: userId
      });
    });

    // Handle joining request-specific rooms for real-time updates
    socket.on('join_request', async (data) => {
      try {
        const { requestId } = data;

        const request = await Request.findById(requestId);
        if (!request) {
          return socket.emit('error', { message: 'Request not found' });
        }

        // Check if user has permission to join
        const isOwner = request.creator.toString() === userId;
        const isAcceptedDonor = request.acceptedBy.some(
          acceptance => acceptance.donor.toString() === userId
        );

        if (isOwner || isAcceptedDonor || user.role === 'admin') {
          socket.join(`request_${requestId}`);
          socket.emit('joined_request', { requestId });
        } else {
          socket.emit('error', { message: 'Permission denied to join request' });
        }

      } catch (error) {
        console.error('Join request error:', error);
        socket.emit('error', { message: 'Failed to join request' });
      }
    });

    // Handle leaving request rooms
    socket.on('leave_request', (data) => {
      const { requestId } = data;
      socket.leave(`request_${requestId}`);
      socket.emit('left_request', { requestId });
    });

    // Handle marking notifications as read
    socket.on('mark_notification_read', async (data) => {
      try {
        const { notificationId } = data;

        const notification = await Notification.findOne({
          _id: notificationId,
          recipient: userId
        });

        if (notification) {
          await notification.markAsRead();
          socket.emit('notification_marked_read', { notificationId });

          // Send updated unread count
          const unreadCount = await Notification.getUnreadCount(userId);
          socket.emit('unread_notifications_count', { count: unreadCount });
        }

      } catch (error) {
        console.error('Mark notification read error:', error);
        socket.emit('error', { message: 'Failed to mark notification as read' });
      }
    });

    // Handle activity tracking
    socket.on('activity', () => {
      const connection = activeConnections.get(userId);
      if (connection) {
        connection.lastActivity = new Date();
      }
    });

    // Handle disconnect
    socket.on('disconnect', async (reason) => {
      console.log(`🔌 Socket disconnected: ${socket.id} for user ${user.name} (${reason})`);

      // Remove from active connections
      activeConnections.delete(userId);

      // Update last active time
      await user.updateLastActive();
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });

  // Periodic cleanup of inactive connections
  setInterval(() => {
    const now = new Date();
    const timeout = 30 * 60 * 1000; // 30 minutes

    for (const [userId, connection] of activeConnections.entries()) {
      if (now - connection.lastActivity > timeout) {
        console.log(`🧹 Cleaning up inactive connection for user ${userId}`);
        activeConnections.delete(userId);
      }
    }
  }, 5 * 60 * 1000); // Check every 5 minutes
};

// Helper function to notify nearby requests when donor becomes available
const notifyNearbyRequests = async (donor, io) => {
  try {
    const nearbyRequests = await Request.find({
      bloodGroup: donor.bloodGroup,
      status: 'pending',
      isActive: true,
      expiresAt: { $gt: new Date() },
      location: {
        $near: {
          $geometry: donor.location,
          $maxDistance: 25000 // 25km
        }
      }
    }).populate('creator', 'name');

    nearbyRequests.forEach(request => {
      io.to(`user_${request.creator._id}`).emit('donor_available', {
        donor: {
          id: donor._id,
          name: donor.name,
          bloodGroup: donor.bloodGroup,
          location: donor.location
        },
        requestId: request._id
      });
    });

  } catch (error) {
    console.error('Error notifying nearby requests:', error);
  }
};

// Export active connections for use in other modules
const getActiveConnections = () => activeConnections;

module.exports = {
  initSockets,
  getActiveConnections
};
