// models/Donation.js
const mongoose = require('mongoose');

const DonationSchema = new mongoose.Schema({
  // Core donation information
  request: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Request',
    required: [true, 'Associated request is required']
  },

  donor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Donor information is required']
  },

  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Recipient information is required']
  },

  // Donation details
  bloodGroup: {
    type: String,
    required: [true, 'Blood group is required'],
    enum: ['A+','A-','B+','B-','AB+','AB-','O+','O-']
  },

  unitsCollected: {
    type: Number,
    required: [true, 'Units collected is required'],
    min: [1, 'At least 1 unit must be collected'],
    max: [4, 'Maximum 4 units can be collected in one session']
  },

  // Donation process tracking
  status: {
    type: String,
    enum: {
      values: ['scheduled', 'in_progress', 'completed', 'cancelled', 'failed'],
      message: 'Invalid donation status'
    },
    default: 'scheduled'
  },

  // Timeline
  scheduledAt: { type: Date, required: true },
  startedAt: Date,
  completedAt: Date,
  donatedAt: { type: Date, default: Date.now },

  // Location information
  donationCenter: {
    name: { type: String, required: true },
    address: {
      street: String,
      city: { type: String, required: true },
      state: { type: String, required: true },
      country: { type: String, default: 'India' },
      pincode: String
    },
    location: {
      type: { type: String, enum: ['Point'], default: 'Point' },
      coordinates: { type: [Number], required: true }
    },
    contactInfo: {
      phone: String,
      email: String
    }
  },

  // Medical information
  medicalInfo: {
    donorWeight: { type: Number, required: true, min: 45 },
    bloodPressure: {
      systolic: { type: Number, min: 90, max: 180 },
      diastolic: { type: Number, min: 60, max: 110 }
    },
    hemoglobin: { type: Number, min: 12.5, max: 20 },
    temperature: { type: Number, min: 96, max: 100 },
    pulse: { type: Number, min: 50, max: 120 },

    // Pre-donation screening
    screeningPassed: { type: Boolean, default: false },
    screeningNotes: String,
    screenedBy: String, // Medical staff name

    // Post-donation
    adverseReactions: [String],
    postDonationNotes: String
  },

  // Blood bag information
  bloodBag: {
    bagId: { type: String, unique: true, sparse: true },
    expiryDate: Date,
    storageLocation: String,
    bloodBankId: String,
    testResults: {
      hiv: { type: String, enum: ['negative', 'positive', 'pending'] },
      hepatitisB: { type: String, enum: ['negative', 'positive', 'pending'] },
      hepatitisC: { type: String, enum: ['negative', 'positive', 'pending'] },
      syphilis: { type: String, enum: ['negative', 'positive', 'pending'] },
      malaria: { type: String, enum: ['negative', 'positive', 'pending'] },
      overallStatus: {
        type: String,
        enum: ['safe', 'unsafe', 'pending'],
        default: 'pending'
      },
      testedAt: Date,
      testedBy: String
    }
  },

  // Confirmation and verification
  confirmedByRecipient: { type: Boolean, default: false },
  confirmedByDonor: { type: Boolean, default: false },
  verifiedByMedicalStaff: { type: Boolean, default: false },

  confirmationDetails: {
    recipientConfirmedAt: Date,
    donorConfirmedAt: Date,
    medicalStaffConfirmedAt: Date,
    verificationCode: String
  },

  // Feedback and ratings
  feedback: {
    donorRating: { type: Number, min: 1, max: 5 },
    donorFeedback: String,
    recipientRating: { type: Number, min: 1, max: 5 },
    recipientFeedback: String,
    centerRating: { type: Number, min: 1, max: 5 },
    centerFeedback: String
  },

  // Administrative
  donationCertificate: {
    certificateNumber: String,
    issuedAt: Date,
    downloadUrl: String
  },

  // Emergency donation flag
  isEmergencyDonation: { type: Boolean, default: false },

  // Notes and additional information
  notes: String,
  adminNotes: String,

  // Tracking and analytics
  responseTime: Number, // Time from request to donation acceptance (in minutes)
  completionTime: Number, // Time from acceptance to completion (in minutes)

  // Cancellation information
  cancellationReason: String,
  cancelledBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  cancelledAt: Date

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
DonationSchema.index({ donor: 1 });
DonationSchema.index({ recipient: 1 });
DonationSchema.index({ request: 1 });
DonationSchema.index({ bloodGroup: 1 });
DonationSchema.index({ status: 1 });
DonationSchema.index({ donatedAt: -1 });
DonationSchema.index({ scheduledAt: 1 });
DonationSchema.index({ 'bloodBag.bagId': 1 });

// Virtual for donation duration
DonationSchema.virtual('donationDuration').get(function() {
  if (this.startedAt && this.completedAt) {
    return this.completedAt.getTime() - this.startedAt.getTime();
  }
  return null;
});

// Virtual for blood safety status
DonationSchema.virtual('isSafe').get(function() {
  return this.bloodBag && this.bloodBag.testResults &&
         this.bloodBag.testResults.overallStatus === 'safe';
});

// Pre-save middleware
DonationSchema.pre('save', function(next) {
  // Generate bag ID if not present
  if (this.isNew && !this.bloodBag.bagId) {
    this.bloodBag.bagId = `BB${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;
  }

  // Set expiry date for blood bag (35 days from collection)
  if (this.isNew && this.donatedAt) {
    const expiryDate = new Date(this.donatedAt);
    expiryDate.setDate(expiryDate.getDate() + 35);
    this.bloodBag.expiryDate = expiryDate;
  }

  // Calculate response and completion times
  if (this.isNew && this.request) {
    // These would be calculated based on request creation time
    // Implementation would require populating request data
  }

  next();
});

// Method to mark as completed
DonationSchema.methods.markCompleted = function() {
  this.status = 'completed';
  this.completedAt = new Date();
  this.confirmedByDonor = true;
  return this.save();
};

// Method to confirm by recipient
DonationSchema.methods.confirmByRecipient = function(rating, feedback) {
  this.confirmedByRecipient = true;
  this.confirmationDetails.recipientConfirmedAt = new Date();

  if (rating) this.feedback.recipientRating = rating;
  if (feedback) this.feedback.recipientFeedback = feedback;

  return this.save();
};

// Static method to get donation statistics
DonationSchema.statics.getStatistics = function(userId, userRole) {
  const matchCondition = userRole === 'donor' ? { donor: userId } : { recipient: userId };

  return this.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: null,
        totalDonations: { $sum: 1 },
        totalUnits: { $sum: '$unitsCollected' },
        completedDonations: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        averageRating: {
          $avg: userRole === 'donor' ? '$feedback.donorRating' : '$feedback.recipientRating'
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Donation', DonationSchema);
