// models/Donation.js
const mongoose = require('mongoose');

const DonationSchema = new mongoose.Schema({
  request: { type: mongoose.Schema.Types.ObjectId, ref: 'Request', required: true },
  donor: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  donatedAt: { type: Date, default: Date.now },
  confirmedByRecipient: { type: Boolean, default: false }
}, { timestamps: true });

module.exports = mongoose.model('Donation', DonationSchema);
