// pages/Auth/Register.jsx
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { HeartIcon } from '@heroicons/react/24/outline';

const Register = () => {
  const [step, setStep] = useState(1);
  const [role, setRole] = useState('');

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center">
              <HeartIcon className="h-8 w-8 text-white" />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Join <PERSON>Saver
          </h2>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Create your account to start saving lives
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow-lg rounded-lg">
          {step === 1 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white text-center">
                Choose your role
              </h3>
              
              <div className="space-y-4">
                <button
                  onClick={() => {
                    setRole('donor');
                    setStep(2);
                  }}
                  className="w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-left"
                >
                  <div className="font-medium text-gray-900 dark:text-white">Blood Donor</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Donate blood to help save lives
                  </div>
                </button>
                
                <button
                  onClick={() => {
                    setRole('patient');
                    setStep(2);
                  }}
                  className="w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-left"
                >
                  <div className="font-medium text-gray-900 dark:text-white">Patient</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Request blood for medical needs
                  </div>
                </button>
                
                <button
                  onClick={() => {
                    setRole('hospital');
                    setStep(2);
                  }}
                  className="w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-left"
                >
                  <div className="font-medium text-gray-900 dark:text-white">Hospital</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Manage blood requests for patients
                  </div>
                </button>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Registration Form
                </h3>
                <button
                  onClick={() => setStep(1)}
                  className="text-sm text-red-600 hover:text-red-500"
                >
                  Change Role
                </button>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <p className="text-sm text-yellow-800">
                  Registration form for {role} is under development. 
                  Please check back soon!
                </p>
              </div>
            </div>
          )}

          <div className="mt-6 text-center">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Already have an account?{' '}
              <Link to="/login" className="font-medium text-red-600 hover:text-red-500">
                Sign in
              </Link>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
