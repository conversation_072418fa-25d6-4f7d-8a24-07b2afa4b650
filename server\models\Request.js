// models/Request.js
const mongoose = require('mongoose');

const RequestSchema = new mongoose.Schema({
  creator: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // patient or hospital
  bloodGroup: { type: String, required: true },
  urgency: { type: String, enum: ['normal','critical'], default: 'normal' },
  units: { type: Number, default: 1 },
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], required: true } // [lng, lat]
  },
  status: { type: String, enum: ['pending','accepted','in_progress','completed','closed'], default: 'pending' },
  acceptedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  acceptedAt: Date,
  closedAt: Date,
  notes: String
}, { timestamps: true });

RequestSchema.index({ location: '2dsphere' });

module.exports = mongoose.model('Request', RequestSchema);
