// models/Request.js
const mongoose = require('mongoose');

const RequestSchema = new mongoose.Schema({
  // Basic request information
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Request creator is required']
  },

  bloodGroup: {
    type: String,
    required: [true, 'Blood group is required'],
    enum: {
      values: ['A+','A-','B+','B-','AB+','AB-','O+','O-'],
      message: 'Invalid blood group'
    }
  },

  urgency: {
    type: String,
    enum: {
      values: ['normal', 'urgent', 'critical', 'emergency'],
      message: 'Urgency must be normal, urgent, critical, or emergency'
    },
    default: 'normal'
  },

  units: {
    type: Number,
    required: [true, 'Number of units is required'],
    min: [1, 'At least 1 unit is required'],
    max: [10, 'Maximum 10 units can be requested at once']
  },

  // Patient information
  patientInfo: {
    name: { type: String, required: true },
    age: { type: Number, required: true, min: 0, max: 120 },
    gender: {
      type: String,
      enum: ['male', 'female', 'other'],
      required: true
    },
    weight: { type: Number, min: 1 },
    medicalCondition: { type: String, required: true },
    doctorName: String,
    hospitalAdmissionId: String
  },

  // Location information
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: {
      type: [Number],
      required: [true, 'Location coordinates are required'],
      validate: {
        validator: function(coords) {
          return coords.length === 2 &&
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates'
      }
    }
  },

  address: {
    hospitalName: { type: String, required: true },
    street: String,
    city: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, default: 'India' },
    pincode: String,
    landmark: String
  },

  // Request status and tracking
  status: {
    type: String,
    enum: {
      values: ['pending', 'accepted', 'in_progress', 'completed', 'cancelled', 'expired'],
      message: 'Invalid status'
    },
    default: 'pending'
  },

  // Donor information
  acceptedBy: [{
    donor: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    acceptedAt: { type: Date, default: Date.now },
    status: {
      type: String,
      enum: ['accepted', 'confirmed', 'donated', 'cancelled'],
      default: 'accepted'
    },
    unitsCommitted: { type: Number, min: 1 },
    notes: String
  }],

  // Timeline tracking
  timeline: [{
    action: {
      type: String,
      enum: ['created', 'accepted', 'confirmed', 'donated', 'completed', 'cancelled', 'expired'],
      required: true
    },
    timestamp: { type: Date, default: Date.now },
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    notes: String
  }],

  // Request details
  requiredBy: {
    type: Date,
    required: [true, 'Required by date is mandatory'],
    validate: {
      validator: function(date) {
        return date > new Date();
      },
      message: 'Required by date must be in the future'
    }
  },

  description: {
    type: String,
    required: [true, 'Request description is required'],
    maxlength: [500, 'Description cannot exceed 500 characters']
  },

  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },

  // Contact information
  contactInfo: {
    primaryContact: {
      name: { type: String, required: true },
      phone: { type: String, required: true },
      email: String,
      relation: {
        type: String,
        enum: ['self', 'family', 'friend', 'doctor', 'hospital_staff'],
        default: 'self'
      }
    },
    alternateContact: {
      name: String,
      phone: String,
      email: String,
      relation: String
    }
  },

  // Fulfillment tracking
  fulfillment: {
    totalUnitsReceived: { type: Number, default: 0 },
    totalUnitsRequired: { type: Number, required: true },
    isFullyFulfilled: { type: Boolean, default: false },
    completedAt: Date,
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  },

  // Request metadata
  priority: {
    type: Number,
    default: 1,
    min: 1,
    max: 10 // 10 being highest priority
  },

  isEmergency: { type: Boolean, default: false },
  isActive: { type: Boolean, default: true },
  expiresAt: {
    type: Date,
    default: function() {
      // Default expiry: 7 days from creation
      return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    }
  },

  // Analytics and tracking
  viewCount: { type: Number, default: 0 },
  shareCount: { type: Number, default: 0 },
  responseCount: { type: Number, default: 0 },

  // Notifications sent
  notificationsSent: [{
    type: { type: String, enum: ['email', 'sms', 'push', 'broadcast'] },
    sentAt: { type: Date, default: Date.now },
    recipientCount: Number,
    status: { type: String, enum: ['sent', 'failed', 'pending'] }
  }]

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
RequestSchema.index({ location: '2dsphere' });
RequestSchema.index({ bloodGroup: 1 });
RequestSchema.index({ status: 1 });
RequestSchema.index({ urgency: 1 });
RequestSchema.index({ creator: 1 });
RequestSchema.index({ requiredBy: 1 });
RequestSchema.index({ isActive: 1 });
RequestSchema.index({ expiresAt: 1 });
RequestSchema.index({ createdAt: -1 });

// Compound indexes
RequestSchema.index({ bloodGroup: 1, status: 1, isActive: 1 });
RequestSchema.index({ location: '2dsphere', bloodGroup: 1, status: 1 });

// Virtual for time remaining
RequestSchema.virtual('timeRemaining').get(function() {
  if (!this.requiredBy) return null;
  const now = new Date();
  const remaining = this.requiredBy.getTime() - now.getTime();
  return remaining > 0 ? remaining : 0;
});

// Virtual for urgency level
RequestSchema.virtual('urgencyLevel').get(function() {
  const timeRemaining = this.timeRemaining;
  if (!timeRemaining) return 'expired';

  const hoursRemaining = timeRemaining / (1000 * 60 * 60);

  if (this.urgency === 'emergency' || hoursRemaining <= 2) return 'emergency';
  if (this.urgency === 'critical' || hoursRemaining <= 12) return 'critical';
  if (this.urgency === 'urgent' || hoursRemaining <= 24) return 'urgent';
  return 'normal';
});

// Pre-save middleware
RequestSchema.pre('save', function(next) {
  // Set fulfillment total units required
  if (this.isNew) {
    this.fulfillment.totalUnitsRequired = this.units;

    // Add creation to timeline
    this.timeline.push({
      action: 'created',
      user: this.creator,
      notes: 'Blood request created'
    });
  }

  // Check if fully fulfilled
  if (this.fulfillment.totalUnitsReceived >= this.fulfillment.totalUnitsRequired) {
    this.fulfillment.isFullyFulfilled = true;
    if (!this.fulfillment.completedAt) {
      this.fulfillment.completedAt = new Date();
    }
  }

  next();
});

// Method to add donor acceptance
RequestSchema.methods.addDonorAcceptance = function(donorId, unitsCommitted, notes) {
  this.acceptedBy.push({
    donor: donorId,
    unitsCommitted: unitsCommitted,
    notes: notes
  });

  this.timeline.push({
    action: 'accepted',
    user: donorId,
    notes: `Donor committed ${unitsCommitted} units`
  });

  this.responseCount += 1;

  if (this.status === 'pending') {
    this.status = 'accepted';
  }
};

// Method to mark donation as completed
RequestSchema.methods.completeDonation = function(donorId, unitsReceived) {
  const acceptance = this.acceptedBy.find(acc => acc.donor.toString() === donorId.toString());
  if (acceptance) {
    acceptance.status = 'donated';
  }

  this.fulfillment.totalUnitsReceived += unitsReceived;

  this.timeline.push({
    action: 'donated',
    user: donorId,
    notes: `${unitsReceived} units received`
  });

  if (this.fulfillment.isFullyFulfilled) {
    this.status = 'completed';
    this.timeline.push({
      action: 'completed',
      notes: 'Request fully fulfilled'
    });
  }
};

module.exports = mongoose.model('Request', RequestSchema);
