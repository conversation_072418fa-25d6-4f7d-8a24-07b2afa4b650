import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import "./index.css"; 
function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      {/* Heading with blue color */}
      <h1 className="text-3xl font-bold text-blue-600">
        Welcome to Vite + React
      </h1>

      {/* Paragraph with gray text */}
      <p className="mt-4 text-gray-700">
        This is a simple template to get you started with Vite and React.
      </p>

      {/* Example button with Tailwind styles */}
      <button
        onClick={() => setCount(count + 1)}
        className="mt-6 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition"
      >
        Count is {count}
      </button>
    </>
  )
}

export default App
