// store/slices/uiSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Navigation
  sidebarOpen: false,
  mobileMenuOpen: false,
  
  // Modals
  modals: {
    createRequest: false,
    editProfile: false,
    changePassword: false,
    confirmDialog: false,
    donationDetails: false,
    requestDetails: false,
  },
  
  // Loading states
  globalLoading: false,
  
  // Notifications/Toasts
  toasts: [],
  
  // Theme
  theme: localStorage.getItem('theme') || 'light',
  
  // Map settings
  mapSettings: {
    center: [28.6139, 77.2090], // Delhi coordinates as default
    zoom: 10,
    showDonors: true,
    showRequests: true,
    showHospitals: true,
  },
  
  // Filters panel
  filtersOpen: false,
  
  // Current location
  userLocation: null,
  locationPermission: null, // 'granted', 'denied', 'prompt'
  
  // Search
  searchQuery: '',
  searchResults: [],
  searchLoading: false,
  
  // Pagination
  currentPage: 1,
  itemsPerPage: 20,
  
  // Confirmation dialog
  confirmDialog: {
    open: false,
    title: '',
    message: '',
    onConfirm: null,
    onCancel: null,
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    type: 'info', // 'info', 'warning', 'error', 'success'
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Navigation
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.sidebarOpen = action.payload;
    },
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen;
    },
    setMobileMenuOpen: (state, action) => {
      state.mobileMenuOpen = action.payload;
    },
    
    // Modals
    openModal: (state, action) => {
      const { modalName, data } = action.payload;
      state.modals[modalName] = true;
      if (data) {
        state.modalData = { ...state.modalData, [modalName]: data };
      }
    },
    closeModal: (state, action) => {
      const modalName = action.payload;
      state.modals[modalName] = false;
      if (state.modalData && state.modalData[modalName]) {
        delete state.modalData[modalName];
      }
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key] = false;
      });
      state.modalData = {};
    },
    
    // Loading
    setGlobalLoading: (state, action) => {
      state.globalLoading = action.payload;
    },
    
    // Toasts
    addToast: (state, action) => {
      const toast = {
        id: Date.now() + Math.random(),
        type: 'info',
        duration: 5000,
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    removeToast: (state, action) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },
    clearToasts: (state) => {
      state.toasts = [];
    },
    
    // Theme
    setTheme: (state, action) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    toggleTheme: (state) => {
      const newTheme = state.theme === 'light' ? 'dark' : 'light';
      state.theme = newTheme;
      localStorage.setItem('theme', newTheme);
    },
    
    // Map
    setMapCenter: (state, action) => {
      state.mapSettings.center = action.payload;
    },
    setMapZoom: (state, action) => {
      state.mapSettings.zoom = action.payload;
    },
    updateMapSettings: (state, action) => {
      state.mapSettings = { ...state.mapSettings, ...action.payload };
    },
    
    // Filters
    toggleFilters: (state) => {
      state.filtersOpen = !state.filtersOpen;
    },
    setFiltersOpen: (state, action) => {
      state.filtersOpen = action.payload;
    },
    
    // Location
    setUserLocation: (state, action) => {
      state.userLocation = action.payload;
    },
    setLocationPermission: (state, action) => {
      state.locationPermission = action.payload;
    },
    
    // Search
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    setSearchResults: (state, action) => {
      state.searchResults = action.payload;
    },
    setSearchLoading: (state, action) => {
      state.searchLoading = action.payload;
    },
    clearSearch: (state) => {
      state.searchQuery = '';
      state.searchResults = [];
      state.searchLoading = false;
    },
    
    // Pagination
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action) => {
      state.itemsPerPage = action.payload;
    },
    
    // Confirmation dialog
    openConfirmDialog: (state, action) => {
      state.confirmDialog = {
        open: true,
        ...action.payload,
      };
    },
    closeConfirmDialog: (state) => {
      state.confirmDialog = {
        ...initialState.confirmDialog,
        open: false,
      };
    },
  },
});

export const {
  // Navigation
  toggleSidebar,
  setSidebarOpen,
  toggleMobileMenu,
  setMobileMenuOpen,
  
  // Modals
  openModal,
  closeModal,
  closeAllModals,
  
  // Loading
  setGlobalLoading,
  
  // Toasts
  addToast,
  removeToast,
  clearToasts,
  
  // Theme
  setTheme,
  toggleTheme,
  
  // Map
  setMapCenter,
  setMapZoom,
  updateMapSettings,
  
  // Filters
  toggleFilters,
  setFiltersOpen,
  
  // Location
  setUserLocation,
  setLocationPermission,
  
  // Search
  setSearchQuery,
  setSearchResults,
  setSearchLoading,
  clearSearch,
  
  // Pagination
  setCurrentPage,
  setItemsPerPage,
  
  // Confirmation dialog
  openConfirmDialog,
  closeConfirmDialog,
} = uiSlice.actions;

export default uiSlice.reducer;
