// store/slices/donationsSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Async thunks
export const fetchDonations = createAsyncThunk(
  'donations/fetchDonations',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/donations', { params });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch donations');
    }
  }
);

export const fetchDonationById = createAsyncThunk(
  'donations/fetchDonationById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/donations/${id}`);
      return response.data.data.donation;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch donation');
    }
  }
);

export const createDonation = createAsyncThunk(
  'donations/createDonation',
  async (donationData, { rejectWithValue }) => {
    try {
      const response = await api.post('/donations', donationData);
      return response.data.data.donation;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create donation');
    }
  }
);

export const updateDonationStatus = createAsyncThunk(
  'donations/updateDonationStatus',
  async ({ id, status, notes, medicalInfo, testResults }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/donations/${id}/status`, {
        status,
        notes,
        medicalInfo,
        testResults
      });
      return response.data.data.donation;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update donation status');
    }
  }
);

export const confirmDonation = createAsyncThunk(
  'donations/confirmDonation',
  async ({ id, rating, feedback }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/donations/${id}/confirm`, {
        rating,
        feedback
      });
      return response.data.data.donation;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to confirm donation');
    }
  }
);

const initialState = {
  donations: [],
  currentDonation: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  },
  filters: {
    status: '',
    bloodGroup: '',
    donorId: '',
    recipientId: ''
  },
  createDonationSuccess: false,
  updateStatusSuccess: false,
  confirmDonationSuccess: false,
};

const donationsSlice = createSlice({
  name: 'donations',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentDonation: (state) => {
      state.currentDonation = null;
    },
    clearCreateDonationSuccess: (state) => {
      state.createDonationSuccess = false;
    },
    clearUpdateStatusSuccess: (state) => {
      state.updateStatusSuccess = false;
    },
    clearConfirmDonationSuccess: (state) => {
      state.confirmDonationSuccess = false;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        status: '',
        bloodGroup: '',
        donorId: '',
        recipientId: ''
      };
    },
    addNewDonation: (state, action) => {
      state.donations.unshift(action.payload);
    },
    updateDonationInList: (state, action) => {
      const index = state.donations.findIndex(donation => donation._id === action.payload._id);
      if (index !== -1) {
        state.donations[index] = action.payload;
      }
    },
    removeDonationFromList: (state, action) => {
      state.donations = state.donations.filter(donation => donation._id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch donations
      .addCase(fetchDonations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDonations.fulfilled, (state, action) => {
        state.loading = false;
        state.donations = action.payload.donations;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchDonations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Fetch donation by ID
      .addCase(fetchDonationById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDonationById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentDonation = action.payload;
        state.error = null;
      })
      .addCase(fetchDonationById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create donation
      .addCase(createDonation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createDonation.fulfilled, (state, action) => {
        state.loading = false;
        state.donations.unshift(action.payload);
        state.createDonationSuccess = true;
        state.error = null;
      })
      .addCase(createDonation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update donation status
      .addCase(updateDonationStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateDonationStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.currentDonation = action.payload;
        state.updateStatusSuccess = true;
        state.error = null;
        
        // Update in donations list if present
        const index = state.donations.findIndex(donation => donation._id === action.payload._id);
        if (index !== -1) {
          state.donations[index] = action.payload;
        }
      })
      .addCase(updateDonationStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Confirm donation
      .addCase(confirmDonation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(confirmDonation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentDonation = action.payload;
        state.confirmDonationSuccess = true;
        state.error = null;
        
        // Update in donations list if present
        const index = state.donations.findIndex(donation => donation._id === action.payload._id);
        if (index !== -1) {
          state.donations[index] = action.payload;
        }
      })
      .addCase(confirmDonation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  clearError,
  clearCurrentDonation,
  clearCreateDonationSuccess,
  clearUpdateStatusSuccess,
  clearConfirmDonationSuccess,
  setFilters,
  clearFilters,
  addNewDonation,
  updateDonationInList,
  removeDonationFromList,
} = donationsSlice.actions;

export default donationsSlice.reducer;
