// models/Notification.js
const mongoose = require('mongoose');

const NotificationSchema = new mongoose.Schema({
  // Recipient information
  recipient: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: [true, 'Recipient is required']
  },
  
  // Notification content
  title: { 
    type: String, 
    required: [true, 'Notification title is required'],
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  
  message: { 
    type: String, 
    required: [true, 'Notification message is required'],
    maxlength: [500, 'Message cannot exceed 500 characters']
  },
  
  // Notification type and category
  type: {
    type: String,
    enum: {
      values: [
        'blood_request', 'request_accepted', 'donation_scheduled', 
        'donation_completed', 'request_fulfilled', 'emergency_alert',
        'system_update', 'verification', 'reminder', 'achievement'
      ],
      message: 'Invalid notification type'
    },
    required: true
  },
  
  category: {
    type: String,
    enum: {
      values: ['urgent', 'important', 'info', 'success', 'warning', 'error'],
      message: 'Invalid notification category'
    },
    default: 'info'
  },
  
  // Related entities
  relatedRequest: { type: mongoose.Schema.Types.ObjectId, ref: 'Request' },
  relatedDonation: { type: mongoose.Schema.Types.ObjectId, ref: 'Donation' },
  relatedUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  
  // Notification status
  isRead: { type: Boolean, default: false },
  readAt: Date,
  
  // Delivery channels
  channels: {
    push: { 
      sent: { type: Boolean, default: false },
      sentAt: Date,
      status: { type: String, enum: ['pending', 'sent', 'failed'] }
    },
    email: { 
      sent: { type: Boolean, default: false },
      sentAt: Date,
      status: { type: String, enum: ['pending', 'sent', 'failed'] }
    },
    sms: { 
      sent: { type: Boolean, default: false },
      sentAt: Date,
      status: { type: String, enum: ['pending', 'sent', 'failed'] }
    }
  },
  
  // Action information
  actionRequired: { type: Boolean, default: false },
  actionUrl: String,
  actionText: String,
  
  // Metadata
  priority: { 
    type: Number, 
    default: 1,
    min: 1,
    max: 5 // 5 being highest priority
  },
  
  expiresAt: Date,
  isActive: { type: Boolean, default: true },
  
  // Additional data
  data: mongoose.Schema.Types.Mixed, // For storing additional context
  
  // Tracking
  clickCount: { type: Number, default: 0 },
  lastClickedAt: Date
  
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
NotificationSchema.index({ recipient: 1, createdAt: -1 });
NotificationSchema.index({ recipient: 1, isRead: 1 });
NotificationSchema.index({ type: 1 });
NotificationSchema.index({ category: 1 });
NotificationSchema.index({ expiresAt: 1 });
NotificationSchema.index({ isActive: 1 });

// Virtual for time since creation
NotificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now.getTime() - this.createdAt.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  return `${days}d ago`;
});

// Method to mark as read
NotificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

// Method to track click
NotificationSchema.methods.trackClick = function() {
  this.clickCount += 1;
  this.lastClickedAt = new Date();
  return this.save();
};

// Static method to create notification
NotificationSchema.statics.createNotification = async function(data) {
  const notification = new this(data);
  await notification.save();
  
  // Emit socket event for real-time notification
  const io = require('../server').io;
  if (io) {
    io.to(`user_${data.recipient}`).emit('new_notification', notification);
  }
  
  return notification;
};

// Static method to get unread count
NotificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({ 
    recipient: userId, 
    isRead: false, 
    isActive: true 
  });
};

// Static method to mark all as read
NotificationSchema.statics.markAllAsRead = function(userId) {
  return this.updateMany(
    { recipient: userId, isRead: false },
    { isRead: true, readAt: new Date() }
  );
};

// Pre-save middleware
NotificationSchema.pre('save', function(next) {
  // Set expiry date if not provided (default: 30 days)
  if (this.isNew && !this.expiresAt) {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30);
    this.expiresAt = expiryDate;
  }
  
  next();
});

module.exports = mongoose.model('Notification', NotificationSchema);
